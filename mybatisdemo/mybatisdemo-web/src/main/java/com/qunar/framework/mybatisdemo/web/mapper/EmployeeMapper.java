package com.qunar.framework.mybatisdemo.web.mapper;

import com.qunar.framework.mybatisdemo.web.entity.Employee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工信息Mapper接口
 */
@Mapper
public interface EmployeeMapper {

    /**
     * 根据ID查询员工信息
     * @param id 员工ID
     * @return 员工信息
     */
    Employee selectById(@Param("id") Integer id);

    /**
     * 根据员工工号查询员工信息
     * @param staffId 员工工号
     * @return 员工信息
     */
    Employee selectByStaffId(@Param("staffId") Integer staffId);

    /**
     * 查询所有员工信息
     * @return 员工信息列表
     */
    List<Employee> selectAll();

    /**
     * 根据地区查询员工信息
     * @param area 工作地区
     * @return 员工信息列表
     */
    List<Employee> selectByArea(@Param("area") String area);

    /**
     * 根据在职状态查询员工信息
     * @param isValid 在职状态 1:在职 2:离职
     * @return 员工信息列表
     */
    List<Employee> selectByIsValid(@Param("isValid") Integer isValid);

    /**
     * 插入员工信息
     * @param employee 员工信息
     * @return 影响行数
     */
    int insert(Employee employee);

    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 影响行数
     */
    int update(Employee employee);

    /**
     * 根据ID删除员工信息
     * @param id 员工ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);

    /**
     * 根据员工工号删除员工信息
     * @param staffId 员工工号
     * @return 影响行数
     */
    int deleteByStaffId(@Param("staffId") Integer staffId);
}
