package com.qunar.framework.mybatisdemo.web.controller;

import com.qunar.framework.mybatisdemo.web.entity.Employee;
import com.qunar.framework.mybatisdemo.web.service.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工信息控制器
 */
@RestController
@RequestMapping("/api/employee")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    /**
     * 根据ID查询员工信息
     * @param id 员工ID
     * @return 员工信息
     */
    @GetMapping("/{id}")
    public Employee getEmployeeById(@PathVariable Integer id) {
        return employeeService.getEmployeeById(id);
    }

    /**
     * 根据员工工号查询员工信息
     * @param staffId 员工工号
     * @return 员工信息
     */
    @GetMapping("/staff/{staffId}")
    public Employee getEmployeeByStaffId(@PathVariable Integer staffId) {
        return employeeService.getEmployeeByStaffId(staffId);
    }

    /**
     * 查询所有员工信息
     * @return 员工信息列表
     */
    @GetMapping("/all")
    public List<Employee> getAllEmployees() {
        return employeeService.getAllEmployees();
    }

    /**
     * 根据地区查询员工信息
     * @param area 工作地区
     * @return 员工信息列表
     */
    @GetMapping("/area/{area}")
    public List<Employee> getEmployeesByArea(@PathVariable String area) {
        return employeeService.getEmployeesByArea(area);
    }

    /**
     * 根据在职状态查询员工信息
     * @param isValid 在职状态 1:在职 2:离职
     * @return 员工信息列表
     */
    @GetMapping("/status/{isValid}")
    public List<Employee> getEmployeesByIsValid(@PathVariable Integer isValid) {
        return employeeService.getEmployeesByIsValid(isValid);
    }

    /**
     * 添加员工信息
     * @param employee 员工信息
     * @return 操作结果
     */
    @PostMapping
    public String addEmployee(@RequestBody Employee employee) {
        boolean success = employeeService.addEmployee(employee);
        return success ? "添加成功" : "添加失败";
    }

    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 操作结果
     */
    @PutMapping
    public String updateEmployee(@RequestBody Employee employee) {
        boolean success = employeeService.updateEmployee(employee);
        return success ? "更新成功" : "更新失败";
    }

    /**
     * 根据ID删除员工信息
     * @param id 员工ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public String deleteEmployeeById(@PathVariable Integer id) {
        boolean success = employeeService.deleteEmployeeById(id);
        return success ? "删除成功" : "删除失败";
    }
}
