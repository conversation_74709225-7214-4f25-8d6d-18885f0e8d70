package com.qunar.framework.mybatisdemo.web.entity;

import java.io.Serializable;

/**
 * 员工信息实体类
 */
public class Employee implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 员工工号
     */
    private Integer staffId;

    /**
     * 中文姓名
     */
    private String name;

    /**
     * 电话号码
     */
    private String mobile;

    /**
     * 工作地区, 例如北京、上海等
     */
    private String area;

    /**
     * 性别, 1 男 2 女
     */
    private Integer gender;

    /**
     * 1:在职 2:离职
     */
    private Integer isValid;

    // 构造方法
    public Employee() {}

    public Employee(Integer staffId, String name, String mobile, String area, Integer gender, Integer isValid) {
        this.staffId = staffId;
        this.name = name;
        this.mobile = mobile;
        this.area = area;
        this.gender = gender;
        this.isValid = isValid;
    }

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    @Override
    public String toString() {
        return "Employee{" +
                "id=" + id +
                ", staffId=" + staffId +
                ", name='" + name + '\'' +
                ", mobile='" + mobile + '\'' +
                ", area='" + area + '\'' +
                ", gender=" + gender +
                ", isValid=" + isValid +
                '}';
    }
}
