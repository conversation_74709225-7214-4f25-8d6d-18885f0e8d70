package com.qunar.framework.mybatisdemo.web.mapper;

import com.qunar.framework.mybatisdemo.web.entity.LeaveHoliday;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 员工请假Mapper接口
 */
@Mapper
public interface LeaveHolidayMapper {

    /**
     * 根据ID查询请假信息
     * @param id 请假ID
     * @return 请假信息
     */
    LeaveHoliday selectById(@Param("id") Integer id);

    /**
     * 根据员工工号查询请假信息
     * @param staffId 员工工号
     * @return 请假信息列表
     */
    List<LeaveHoliday> selectByStaffId(@Param("staffId") Integer staffId);

    /**
     * 查询所有请假信息
     * @return 请假信息列表
     */
    List<LeaveHoliday> selectAll();

    /**
     * 根据地区查询请假信息
     * @param area 工作地区
     * @return 请假信息列表
     */
    List<LeaveHoliday> selectByArea(@Param("area") String area);

    /**
     * 根据请假类型查询请假信息
     * @param type 请假类型 1:年假 2:病假
     * @return 请假信息列表
     */
    List<LeaveHoliday> selectByType(@Param("type") Integer type);

    /**
     * 根据时间范围查询请假信息
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 请假信息列表
     */
    List<LeaveHoliday> selectByDateRange(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据员工工号和时间范围查询请假信息
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 请假信息列表
     */
    List<LeaveHoliday> selectByStaffIdAndDateRange(@Param("staffId") Integer staffId, 
                                                   @Param("startDate") Date startDate, 
                                                   @Param("endDate") Date endDate);

    /**
     * 插入请假信息
     * @param leaveHoliday 请假信息
     * @return 影响行数
     */
    int insert(LeaveHoliday leaveHoliday);

    /**
     * 更新请假信息
     * @param leaveHoliday 请假信息
     * @return 影响行数
     */
    int update(LeaveHoliday leaveHoliday);

    /**
     * 根据ID删除请假信息
     * @param id 请假ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);

    /**
     * 根据员工工号删除请假信息
     * @param staffId 员工工号
     * @return 影响行数
     */
    int deleteByStaffId(@Param("staffId") Integer staffId);
}
