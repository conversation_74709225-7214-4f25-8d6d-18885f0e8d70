package com.qunar.framework.mybatisdemo.web.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 员工请假实体类
 */
public class LeaveHoliday implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 员工工号
     */
    private Integer staffId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 请假天数
     */
    private Integer dayNum;

    /**
     * 1:年假 2:病假
     */
    private Integer type;

    /**
     * 工作地区, 例如北京、上海等
     */
    private String area;

    // 构造方法
    public LeaveHoliday() {}

    public LeaveHoliday(Integer staffId, Date startDate, Date endDate, Integer dayNum, Integer type, String area) {
        this.staffId = staffId;
        this.startDate = startDate;
        this.endDate = endDate;
        this.dayNum = dayNum;
        this.type = type;
        this.area = area;
    }

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getDayNum() {
        return dayNum;
    }

    public void setDayNum(Integer dayNum) {
        this.dayNum = dayNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    @Override
    public String toString() {
        return "LeaveHoliday{" +
                "id=" + id +
                ", staffId=" + staffId +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", dayNum=" + dayNum +
                ", type=" + type +
                ", area='" + area + '\'' +
                '}';
    }
}
