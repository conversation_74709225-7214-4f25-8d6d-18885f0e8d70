<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>qunar.common</groupId>
        <artifactId>qunar-supom-generic</artifactId>
        <version>2.0.7</version>
    </parent>

    <groupId>com.qunar.framework.mybatisdemo</groupId>
    <artifactId>mybatisdemo</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>
    <modules>
            <module>mybatisdemo-web</module>
    </modules>

    <properties>
        <java_target_version>8</java_target_version>
        <java_source_version>8</java_source_version>

        <tcdev.version>5.1.4</tcdev.version>

        <org.springframework.version>5.3.18</org.springframework.version>
        <spring-boot.version>2.6.6</spring-boot.version>
        <spring-cloud.version>3.1.2</spring-cloud.version>

        <logback.version>1.2.3</logback.version>
        <log4j2.version>2.15.0</log4j2.version>
        <com.fasterxml.jackson.version>2.11.1</com.fasterxml.jackson.version>

        <qmonitor.version>2.2.7</qmonitor.version>

        <servlet.servlet-api.version>2.5</servlet.servlet-api.version>
        <javax-annotation.version>1.3.2</javax-annotation.version>

        <junit.junit>4.12</junit.junit>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>qunar.tc</groupId>
                <artifactId>tcdev</artifactId>
                <version>${tcdev.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>mybatisdemo-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.junit}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-commons-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${com.fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunar.flight</groupId>
                <artifactId>qmonitor</artifactId>
                <version>${qmonitor.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java_source_version}</source>
                        <target>${java_target_version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>3.2.3</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
